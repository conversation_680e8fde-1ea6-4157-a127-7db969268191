import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          color: AppTheme.primaryGreen.withOpacity(0.2),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Profile Picture
              GestureDetector(
                onTap: () {
                  // Navigate to coins screen or profile edit
                },
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppTheme.primaryGreen,
                      width: 4,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const CircleAvatar(
                    radius: 56,
                    backgroundColor: AppTheme.lightGreen,
                    child: Icon(
                      Icons.person,
                      size: 60,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // User Name
              Text(
                'Hii Lara!',
                style: AppTextStyles.heading2.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Action Buttons Row 1
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildActionButton('My Orders', Icons.shopping_bag_outlined),
                  _buildActionButton('My Store', Icons.store_outlined),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Action Buttons Row 2
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildActionButton('Rewards', Icons.card_giftcard_outlined),
                  _buildActionButton('Coins', Icons.monetization_on_outlined),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Profile Options List
              _buildProfileOption('Edit Profile', Icons.edit_outlined),
              _buildProfileOption('Change Password', Icons.lock_outline),
              _buildProfileOption('Privacy Settings', Icons.privacy_tip_outlined),
              _buildProfileOption('Notifications', Icons.notifications_outlined),
              _buildProfileOption('Account Settings', Icons.settings_outlined),
              
              const SizedBox(height: 24),
              
              // Logout Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    _showLogoutDialog();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryGreen.withOpacity(0.9),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: const Text(
                    'Log Out',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(String title, IconData icon) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: ElevatedButton(
          onPressed: () {
            // Handle button press
            _handleActionButtonPress(title);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryGreen.withOpacity(0.9),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 15),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          child: Column(
            children: [
              Icon(icon, size: 24),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileOption(String title, IconData icon) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: AppTheme.primaryGreen,
        ),
        title: Text(title),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey,
        ),
        onTap: () {
          // Handle option tap
          _handleProfileOptionTap(title);
        },
      ),
    );
  }

  void _handleActionButtonPress(String action) {
    switch (action) {
      case 'My Orders':
        // Navigate to orders screen
        break;
      case 'My Store':
        // Navigate to store screen
        break;
      case 'Rewards':
        // Navigate to rewards screen
        break;
      case 'Coins':
        // Navigate to coins screen
        break;
    }
  }

  void _handleProfileOptionTap(String option) {
    switch (option) {
      case 'Edit Profile':
        // Navigate to edit profile screen
        break;
      case 'Change Password':
        // Navigate to change password screen
        break;
      case 'Privacy Settings':
        // Navigate to privacy settings screen
        break;
      case 'Notifications':
        // Navigate to notifications settings screen
        break;
      case 'Account Settings':
        // Navigate to account settings screen
        break;
    }
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Logout'),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Handle logout
                _handleLogout();
              },
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }

  void _handleLogout() {
    // Implement logout logic
    // Clear user data, navigate to login screen, etc.
  }
}
