import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';

class SocialScreen extends StatefulWidget {
  const SocialScreen({super.key});

  @override
  State<SocialScreen> createState() => _SocialScreenState();
}

class _SocialScreenState extends State<SocialScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Greenity'),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile Gauge/Circle
            _buildProfileGauge(),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildActionButton('Events', Icons.event),
                _buildActionButton('Friends', Icons.people),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Messages Section
            Text(
              'Messages',
              style: AppTextStyles.heading3,
            ),
            
            const SizedBox(height: 16),
            
            // Message Cards
            _buildMessageCard(),
            
            const SizedBox(height: 16),
            
            // Add more message cards as needed
            _buildMessageCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileGauge() {
    return GestureDetector(
      onTap: () {
        // Navigate to coins screen
      },
      child: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.grey,
            width: 4,
          ),
        ),
        child: Stack(
          children: [
            // Progress circle
            Positioned.fill(
              child: CircularProgressIndicator(
                value: 0.7, // 70% progress
                strokeWidth: 8,
                backgroundColor: Colors.grey[300],
                valueColor: const AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryGreen,
                ),
              ),
            ),
            // Profile image
            Center(
              child: Container(
                width: 100,
                height: 100,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppTheme.lightGreen,
                ),
                child: const Icon(
                  Icons.person,
                  size: 50,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(String title, IconData icon) {
    return ElevatedButton.icon(
      onPressed: () {
        // Handle button press
      },
      icon: Icon(icon),
      label: Text(title),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryGreen.withOpacity(0.9),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  Widget _buildMessageCard() {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info
            Row(
              children: [
                Row(
                  children: [
                    _buildUserAvatar('S'),
                    const SizedBox(width: 8),
                    _buildUserAvatar('A'),
                  ],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Suryasen, Ashi Gupta',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        '30th Jan, 24',
                        style: AppTextStyles.caption,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Message content
            const Text(
              'Hello Greenites🌳, Welcome to my new app which facilitates to the upcycling of the waste♻️ while also generating income💰 to the artists and entrepreneurs.',
              style: TextStyle(fontSize: 14),
            ),
            
            const SizedBox(height: 12),
            
            // Action buttons
            Row(
              children: [
                _buildMessageAction(Icons.favorite_border, ''),
                const SizedBox(width: 16),
                _buildMessageAction(Icons.message_outlined, ''),
                const SizedBox(width: 16),
                _buildMessageAction(Icons.share_outlined, ''),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserAvatar(String initial) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppTheme.primaryGreen.withOpacity(0.2),
        border: Border.all(
          color: AppTheme.primaryGreen,
          width: 2,
        ),
      ),
      child: Center(
        child: Text(
          initial,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
      ),
    );
  }

  Widget _buildMessageAction(IconData icon, String label) {
    return GestureDetector(
      onTap: () {
        // Handle action
      },
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Colors.grey[600],
          ),
          if (label.isNotEmpty) ...[
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
