import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/widgets/custom_search_bar.dart';
import '../widgets/image_slider.dart';
import '../widgets/waste_category_card.dart';
import '../widgets/featured_greenite_card.dart';
import '../widgets/popular_product_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController _searchController = TextEditingController();

  final List<WasteCategory> _wasteCategories = [
    WasteCategory(name: 'Bottles', imagePath: 'assets/images/bottle.png'),
    WasteCategory(name: 'Cardboards', imagePath: 'assets/images/cardboard.png'),
    WasteCategory(name: 'Pipes', imagePath: 'assets/images/pipe.png'),
    WasteCategory(name: 'Woods', imagePath: 'assets/images/wood.png'),
  ];

  final List<Product> _popularProducts = [
    Product(name: 'Bag', price: 500, imagePath: 'assets/images/bag.png'),
    Product(name: 'Shoes', price: 1500, imagePath: 'assets/images/shoes.png'),
    Product(name: 'T-Shirt', price: 700, imagePath: 'assets/images/tshirt.png'),
    Product(name: 'Watch', price: 2000, imagePath: 'assets/images/watch.png'),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Home'),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search Bar
            CustomSearchBar(
              controller: _searchController,
              hintText: 'Search for products, categories...',
            ),
            
            const SizedBox(height: 16),
            
            // Image Slider
            const ImageSlider(),
            
            const SizedBox(height: 24),
            
            // Most Upcycled Waste Section
            Text(
              'Most Upcycled Waste',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: 12),
            
            SizedBox(
              height: 140,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _wasteCategories.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 12),
                    child: WasteCategoryCard(
                      category: _wasteCategories[index],
                      onTap: () {
                        // Handle category tap
                      },
                    ),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Featured Greenites Section
            Text(
              'Featured Greenites',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: 12),
            
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: 4,
                itemBuilder: (context, index) {
                  return const Padding(
                    padding: EdgeInsets.only(right: 12),
                    child: FeaturedGreeniteCard(),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Popular Products Section
            Text(
              'Popular Products',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: 12),
            
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _popularProducts.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 12),
                    child: PopularProductCard(
                      product: _popularProducts[index],
                      onBuyTap: () {
                        // Handle buy button tap
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Data Models
class WasteCategory {
  final String name;
  final String imagePath;

  WasteCategory({required this.name, required this.imagePath});
}

class Product {
  final String name;
  final int price;
  final String imagePath;

  Product({required this.name, required this.price, required this.imagePath});
}
