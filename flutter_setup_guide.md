# Flutter Setup Guide for UpCyclization Migration

## 1. Install Flutter SDK

### Windows:
1. Download Flutter SDK from https://flutter.dev/docs/get-started/install/windows
2. Extract to `C:\flutter`
3. Add `C:\flutter\bin` to your PATH environment variable
4. Run `flutter doctor` to verify installation

### macOS:
```bash
# Using Homebrew (recommended)
brew install flutter

# Or download from https://flutter.dev/docs/get-started/install/macos
```

### Linux:
```bash
# Download and extract Flutter
wget https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.16.0-stable.tar.xz
tar xf flutter_linux_3.16.0-stable.tar.xz
export PATH="$PATH:`pwd`/flutter/bin"
``` 

## 2. Install Required Tools

### Android Studio:
1. Download from https://developer.android.com/studio
2. Install Flutter and Dart plugins
3. Set up Android SDK and emulator

### VS Code (Alternative):
1. Install Flutter extension
2. Install Dart extension

## 3. Verify Installation
```bash
flutter doctor
```

## 4. Create New Flutter Project
```bash
# Navigate to your desired directory
cd "d:\Code Bharat"

# Create new Flutter project
flutter create ecocura_flutter
cd ecocura_flutter

# Test the setup
flutter run
```

## 5. Essential Dependencies for Your App

Add these to your `pubspec.yaml`:

```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  
  # Navigation
  go_router: ^12.1.3
  
  # Firebase
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  firebase_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  
  # Camera & Image
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  
  # ML & AI
  tflite_flutter: ^0.10.4
  image: ^4.1.3
  
  # UI Components
  cached_network_image: ^3.3.0
  carousel_slider: ^4.2.1
  
  # HTTP & Networking
  http: ^1.1.0
  dio: ^5.3.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
```

## 6. Project Structure Setup

Create this folder structure:
```
lib/
├── main.dart
├── app.dart
├── core/
│   ├── constants/
│   ├── theme/
│   ├── utils/
│   └── services/
├── features/
│   ├── home/
│   ├── upcycle/
│   ├── market/
│   ├── social/
│   └── profile/
├── shared/
│   ├── widgets/
│   ├── models/
│   └── providers/
└── assets/
    ├── images/
    ├── icons/
    └── ml_models/
```

## 7. Quick Start Commands

```bash
# Get dependencies
flutter pub get

# Run on device/emulator
flutter run

# Build for release
flutter build apk
flutter build ios
```

## Next Steps:
1. Set up the project structure
2. Configure Firebase
3. Implement navigation
4. Start with UI components
