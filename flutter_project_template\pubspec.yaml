name: ecocura_flutter
description: "Flutter version of UpCyclization - A waste management and upcycling app"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.4.9

  # Navigation
  go_router: ^12.1.3

  # Firebase
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  firebase_firestore: ^4.13.6
  firebase_storage: ^11.5.6

  # Camera & Image Processing
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  image: ^4.1.3

  # ML & AI
  tflite_flutter: ^0.10.4
  tflite_flutter_helper: ^0.3.1

  # UI Components
  cached_network_image: ^3.3.0
  carousel_slider: ^4.2.1
  flutter_staggered_grid_view: ^0.7.0
  shimmer: ^3.0.0

  # HTTP & Networking
  http: ^1.1.0
  dio: ^5.3.2

  # Utilities
  shared_preferences: ^2.2.2
  path_provider: ^2.1.1
  permission_handler: ^11.1.0
  uuid: ^4.2.1
  intl: ^0.19.0

  # Icons & Fonts
  cupertino_icons: ^1.0.2
  google_fonts: ^6.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  mockito: ^5.4.4
  build_runner: ^2.4.7

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/ml_models/

  fonts:
    - family: CustomFont
      fonts:
        - asset: assets/fonts/CustomFont-Regular.ttf
        - asset: assets/fonts/CustomFont-Bold.ttf
          weight: 700
